import { Route, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import { useState, useCallback, useEffect, useMemo } from "react";
import NavigatorBar from "@/components/NavBar";
import { Input, Switch, Toast, Avatar, Popover, Divider } from "antd-mobile";
import FileSelector from "@/components/FATWall_APP/FileSelector";
import del_icon from '@/Resources/icon/del.png';
import del_icon_2 from '@/Resources/icon/del_icon.png';
import UserSelector, { User } from "@/components/FATWall_APP/UserSelector";
import { PreloadImage } from "@/components/Image";
import { useLocation } from "react-router-dom";
import { useRequest } from 'ahooks';
import moreIcon from "@/Resources/filmWall/more.png";
import { createLibrary, CreateLibParams, editLibrary, EditLibParams, deleteLibrary } from '@/api/fatWall';
import { getUserList, UserInfo } from '@/api/fatWallPlayer';
import { modalShow } from '@/components/List';
import add_icon from '@/Resources/icon/add.png';
import { useTheme } from "@/utils/themeDetector";
import backIcon_light from '@/Resources/icon/backIcon_light.png';
import backIcon_dark from '@/Resources/icon/backIcon_dark.png';

// 编辑时传递的媒体库数据类型
interface LibraryEditData {
  lib_id: number;
  name: string;
  tv_visable: number;
  scan_path: string[];
  share2who_list: (string | number)[];
}

// 来源项目类型
interface SourceItem {
  path: string;
  displayName: string;
}

// location state 类型定义
interface LocationState {
  isEdit: boolean;
  libraryData?: LibraryEditData & {
    processedScanPath?: SourceItem[]; // 预处理的扫描路径
  };
  libraryDataCount?: any;
}

const CreateLibrary = (props: { children?: React.ReactNode }) => {
  const { path } = useRouteMatch();
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { isEdit, libraryData } = location.state || { isEdit: false };
  const { libraryDataCount } = location.state || { libraryDataCount: 0 };
  const [moreVisible, setMoreVisible] = useState(false);
  // 基础状态
  const [libraryName, setLibraryName] = useState('');
  const [hasUserInput, setHasUserInput] = useState(false); // 记录用户是否已经输入过
  const [showOnTV, setShowOnTV] = useState(libraryData?.tv_visable === 1 || false);
  const [sources, setSources] = useState<SourceItem[]>(
    // 优先使用预处理的路径，避免闪烁
    libraryData?.processedScanPath ||
    libraryData?.scan_path?.map(path => ({
      path,
      displayName: path // 如果没有预处理路径，使用原始路径
    })) || []
  );
  const [sharedUsers, setSharedUsers] = useState<User[]>([]);
  const { isDarkMode } = useTheme();
  // 保存原始数据用于对比
  const [originalData, ] = useState<LibraryEditData | null>(libraryData || null);

  const [, setErrorMessage] = useState<string | null>(null);
  const [fileSelectorVisible, setFileSelectorVisible] = useState(false);
  const [userSelectorVisible, setUserSelectorVisible] = useState(false);

  // 用户列表状态
  const [allUsers, setAllUsers] = useState<User[]>([]);



  // 获取用户列表
  const fetchUserList = useCallback(async () => {
    try {
      await getUserList((response) => {
        if (response.code === 0 && response.data) {
          // 转换API返回的用户数据为本地格式
          const userList: User[] = response.data.list.map((user: UserInfo) => ({
            id: user.uid,
            name: user.nickname,
            avatar: user.headImg,
            role: user.permission === 'admin' ? '管理员' : '普通用户',
            uid: user.uid,
            permission: user.permission,
            group: user.group,
            status: user.status
          }));
          setAllUsers(userList);
          console.log('获取用户列表成功:', userList);
        } else {
          console.warn('API返回数据格式不正确');
          setAllUsers([]);
        }
      });
    } catch (error) {
      console.error('获取用户列表失败：', error);
      setAllUsers([]);
    }
  }, []);

  // 获取默认媒体库名称
  const getDefaultLibraryName = useCallback(() => {
    if (isEdit && libraryData?.name) {
      return libraryData.name;
    }
    return `媒体库${libraryDataCount + 1}`;
  }, [isEdit, libraryData, libraryDataCount]);

  // 初始化媒体库名称
  useEffect(() => {
    if (!hasUserInput) {
      setLibraryName(getDefaultLibraryName());
    }
  }, [getDefaultLibraryName, hasUserInput]);

  // 处理输入框点击事件
  const handleInputFocus = useCallback(() => {
    if (!hasUserInput && !isEdit) {
      // 首次点击且不是编辑模式时清空
      setLibraryName('');
      setHasUserInput(true);
    }
  }, [hasUserInput, isEdit]);

  // 处理输入框内容变化
  const handleLibraryNameChange = useCallback((value: string) => {
    setLibraryName(value);
    if (!hasUserInput) {
      setHasUserInput(true);
    }
  }, [hasUserInput]);

  // 创建媒体库API
  const { run: submitCreateLibrary, loading: isSubmitting } = useRequest(
    createLibrary,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0) {
          Toast.show({
            content: "创建成功",
            position: "bottom",
            duration: 2000,
          });
          // 返回到媒体库列表页面，传递刷新标志
          history.push({
            pathname: '/filmAndTelevisionWall_app/all',
            state: { needRefreshLibraries: true }
          });
        } else {
          // 处理不同的错误码
          handleCreateError(response.code, response.result);
        }
      },
      onError: (error) => {
        console.error('创建媒体库失败：', error);
        setErrorMessage('网络异常，请重试');
        Toast.show({
          content: '网络异常，请重试',
          position: 'bottom',
          duration: 2000,
        });
      },
    }
  );

  // 编辑媒体库API
  const { run: submitEditLibrary, loading: isEditing } = useRequest(
    editLibrary,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0) {
          Toast.show({
            content: "编辑成功",
            position: "bottom",
            duration: 2000,
          });
          // 返回到媒体库列表页面
          history.push('/filmAndTelevisionWall_app/libraryManagement');
        } else {
          handleCreateError(response.code, response.result);
        }
      },
      onError: (error) => {
        console.error('编辑媒体库失败：', error);
        setErrorMessage('网络异常，请重试');
        Toast.show({
          content: '网络异常，请重试',
          position: 'bottom',
          duration: 2000,
        });
      },
    }
  );

  // 删除媒体库API
  const { run: submitDeleteLibrary } = useRequest(
    deleteLibrary,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0) {
          Toast.show({
            content: "删除成功",
            position: "bottom",
            duration: 2000,
          });
          // 返回到媒体库列表页面
          history.push('/filmAndTelevisionWall_app/libraryManagement');
        } else {
          handleDeleteError(response.code, response.result);
        }
      },
      onError: (error) => {
        console.error('删除媒体库失败：', error);
        Toast.show({
          content: '网络异常，请重试',
          position: 'bottom',
          duration: 2000,
        });
      },
    }
  );

  // 处理创建错误
  const handleCreateError = useCallback((code: number, message: string) => {
    let errorMsg = '';
    switch (code) {
      case 2200:
        errorMsg = '内部错误，请稍后重试';
        break;
      case 2201:
        errorMsg = '请求参数错误：选择的目录不存在或分享目标用户不存在';
        break;
      case 2202:
        errorMsg = '已超过媒体库创建上限';
        break;
      case 2204:
        errorMsg = '该名称的媒体库已存在，请使用其他名称';
        break;
      default:
        errorMsg = message || '创建失败，请重试';
    }
    setErrorMessage(errorMsg);
    Toast.show({
      content: errorMsg,
      position: 'bottom',
      duration: 3000,
    });
  }, []);

  // 处理删除错误
  const handleDeleteError = useCallback((code: number, message: string) => {
    let errorMessage = '删除失败';

    switch (code) {
      case 2203:
        errorMessage = '没有删除该媒体库的权限';
        break;
      case 2205:
        errorMessage = '该媒体库不存在';
        break;
      default:
        errorMessage = message || '删除失败，请重试';
        break;
    }

    Toast.show({
      content: errorMessage,
      position: 'bottom',
      duration: 2000,
    });
  }, []);

  // 删除媒体库处理函数
  const handleDeleteLibrary = useCallback(() => {
    if (!libraryData?.lib_id) {
      Toast.show({
        content: '无法获取媒体库信息',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }

    modalShow(
      '移除媒体库',
      <>将移除当前媒体库，媒体文件不受影响</>,
      (modal) => {
        modal.destroy();
        submitDeleteLibrary({ lib_id: libraryData.lib_id });
      },
      () => {
        // 取消操作，关闭 Popover
        setMoreVisible(false);
      },
      false,
      {
        position: 'bottom',
        okBtnText: '确定',
        okBtnStyle: { backgroundColor: 'var(--adm-color-primary)', color: 'white' }
      }
    );
  }, [libraryData, submitDeleteLibrary, setMoreVisible]);

  // 构建编辑参数（只包含有变化的字段）
  const buildEditParams = useCallback((): EditLibParams | null => {
    if (!originalData || !libraryData?.lib_id) {
      return null;
    }

    const params: EditLibParams = {
      lib_id: libraryData.lib_id
    };

    // 检查名称是否有变化
    if (libraryName.trim() !== originalData.name) {
      params.name = libraryName.trim();
    }

    // 检查电视可见性是否有变化
    const currentTvVisible = showOnTV ? 1 : 0;
    if (currentTvVisible !== originalData.tv_visable) {
      params.tv_visable = currentTvVisible;
    }

    // 检查扫描路径的变化
    const originalPaths = new Set(originalData.scan_path);
    const currentPaths = new Set(sources.map(item => item.path));

    const pathsToAdd = sources.map(item => item.path).filter(path => !originalPaths.has(path));
    const pathsToDelete = originalData.scan_path.filter(path => !currentPaths.has(path));

    if (pathsToAdd.length > 0) {
      params.path_add = pathsToAdd;
    }
    if (pathsToDelete.length > 0) {
      params.path_del = pathsToDelete;
    }

    // 检查分享用户的变化
    const currentUserIds = sharedUsers.map(user => String(user.uid || user.id));
    const originalUserIds = (originalData.share2who_list || []).map(id => String(id));

    const originalUserSet = new Set(originalUserIds);
    const currentUserSet = new Set(currentUserIds);

    const usersToAdd = currentUserIds.filter(id => !originalUserSet.has(id));
    const usersToDelete = originalUserIds.filter(id => !currentUserSet.has(id));

    if (usersToAdd.length > 0) {
      params.share_add = usersToAdd;
    }
    if (usersToDelete.length > 0) {
      params.share_del = usersToDelete;
    }

    // 如果没有任何变化，返回null
    const hasChanges = params.name !== undefined ||
      params.tv_visable !== undefined ||
      params.path_add !== undefined ||
      params.path_del !== undefined ||
      params.share_add !== undefined ||
      params.share_del !== undefined;

    return hasChanges ? params : null;
  }, [originalData, libraryData, libraryName, showOnTV, sources, sharedUsers]);

  // 表单验证
  const validateForm = useCallback((): boolean => {
    setErrorMessage(null);

    // 如果用户没有输入且输入框为空，使用默认名称
    const finalLibraryName = libraryName.trim() || getDefaultLibraryName();

    if (!finalLibraryName.trim()) {
      setErrorMessage('请输入媒体库名称');
      return false;
    }

    if (sources.length === 0) {
      setErrorMessage('请至少添加一个媒体库来源');
      return false;
    }

    return true;
  }, [libraryName, sources, getDefaultLibraryName]);

  const handleAddSource = useCallback(() => {
    // 打开文件选择器弹窗
    setFileSelectorVisible(true);
  }, []);

  const handleSelectPath = useCallback((path: string, displayPath?: string) => {
    // 检查路径是否已存在
    if (sources.some(item => item.path === path)) {
      Toast.show({
        content: '该路径已添加',
        position: 'bottom',
        duration: 1500,
      });
      return;
    }

    // 将选中的路径添加到来源列表，使用displayPath作为显示名称
    const displayName = displayPath || path;
    setSources(prev => [...prev, { path, displayName }]);
    Toast.show({
      content: `已添加来源：${displayName}`,
      position: 'bottom',
      duration: 1500,
    });
  }, [sources]);

  const handleAddUser = useCallback(() => {
    // 打开用户选择器弹窗
    setUserSelectorVisible(true);
  }, []);

  const handleSelectUsers = useCallback((users: User[]) => {
    // 更新已选择的用户列表（直接替换，因为UserSelector已经包含了之前选择的用户）
    setSharedUsers(users);
    Toast.show({
      content: `已选择${users.length}个共享用户`,
      position: 'bottom',
      duration: 1500,
    });
  }, []);

  const handleDeleteSource = useCallback((index: number) => {
    setSources(prev => prev.filter((_, i) => i !== index));
    Toast.show({
      content: '已删除来源',
      position: 'bottom',
      duration: 1000,
    });
  }, []);

  const handleDeleteUser = useCallback((userId: string) => {
    setSharedUsers(prev => prev.filter(user => user.id !== userId));
    Toast.show({
      content: '已删除共享用户',
      position: 'bottom',
      duration: 1000,
    });
  }, []);

  const handleSubmit = useCallback(() => {
    // 表单验证
    if (!validateForm()) {
      return;
    }

    // 获取最终的媒体库名称
    const finalLibraryName = libraryName.trim() || getDefaultLibraryName();

    if (isEdit) {
      // 编辑模式
      const editParams = buildEditParams();

      if (!editParams) {
        Toast.show({
          content: '没有检测到任何变化',
          position: 'bottom',
          duration: 2000,
        });
        return;
      }

      console.log('编辑媒体库参数：', editParams);
      submitEditLibrary(editParams);
    } else {
      // 创建模式
      const createParams: CreateLibParams = {
        name: finalLibraryName,
        tv_visable: showOnTV ? 1 : 0,
        scan_path: sources.map(item => item.path),
        share2who: sharedUsers.length > 0 ? sharedUsers.map(user => Number(user.uid || user.id)) : undefined
      };

      console.log('创建媒体库参数：', createParams);
      submitCreateLibrary(createParams);
    }
  }, [isEdit, libraryName, showOnTV, sources, sharedUsers, validateForm, buildEditParams, submitEditLibrary, submitCreateLibrary, getDefaultLibraryName]);

  // 初始化用户列表
  useEffect(() => {
    fetchUserList();
  }, [fetchUserList]);



  // 当用户列表和媒体库数据都加载完成后，设置分享用户
  useEffect(() => {
    if (isEdit && libraryData?.share2who_list && libraryData.share2who_list.length > 0 && allUsers.length > 0) {
      // 根据share2who_list中的用户ID从用户列表中筛选出对应的用户信息
      const selectedUsers: User[] = libraryData.share2who_list
        .map(userId => allUsers.find(user => user.id === String(userId) || user.uid === String(userId)))
        .filter((user): user is User => user !== undefined);

      setSharedUsers(selectedUsers);
      console.log('设置分享用户:', selectedUsers);
    }
  }, [isEdit, libraryData, allUsers]);

  const rightSize = useMemo(() => {
    // 只在编辑模式下显示删除按钮
    if (!isEdit) {
      return null;
    }

    return (
      <div className={styles.right}>
        <Popover
          className={styles.morePopoverContainer}
          visible={moreVisible}
          onVisibleChange={setMoreVisible}
          content={

            <div className={styles.morePopover}>
              <p style={{ fontSize: '16px', color: 'red' }} onClick={handleDeleteLibrary}>删除</p>
            </div>
          }
          trigger='click'
          placement='bottom-end'
          style={{ '--arrow-size': '0px' } as React.CSSProperties}
        >
          <PreloadImage style={{ width: '30px', height: '30px' }} src={moreIcon} alt="more" onClick={() => setMoreVisible(true)} />
        </Popover>
      </div>
    )
  }, [isEdit, moreVisible, handleDeleteLibrary])

  return (
    <>
      <NavigatorBar backIcon={isDarkMode ? backIcon_dark : backIcon_light} styles={{ paddingLeft: '15px',backgroundColor: 'var(--background-color)'}}  onBack={() => history.goBack()} right={rightSize} />

      <div className={styles.container}>
        {/* {props.children} */}
        <Route exact path={path}>

          <div className={styles.title}>{isEdit ? '编辑媒体库' : '创建媒体库'}</div>

          <div className={styles.formContainer}>
            {/* 媒体库名称 */}
            <div className={styles.input}>
              <Input
                className={styles.inputField}
                value={libraryName}
                onChange={handleLibraryNameChange}
                onFocus={handleInputFocus}
                placeholder="请输入媒体库名称"
                clearable
              />
            </div>

            {/* 电视端显示开关 */}
            <div className={styles.formItem}>
              <div className={styles.switchRow}>
                <span className={styles.switchLabel}>允许当前媒体库在电视端显示</span>
                <Switch
                  checked={showOnTV}
                  onChange={setShowOnTV}
                />
              </div>
            </div>
            <div className={styles.divider}>
              <Divider />
            </div>
            {/* 来源列表 */}
            <div className={styles.sectionTitle}>来源</div>
            {/* 添加来源按钮 */}
            <div className={styles.addButton} onClick={handleAddSource}>
              <span>添加媒体库来源</span>
              <PreloadImage src={add_icon || ''} alt='add_icon' style={{ width: '24px', height: '24px' }} />
            </div>
            <div>
              {sources.map((source, index) => (
                <div key={index} className={styles.sourceItem}>
                  <span className={styles.sourceText}>{source.displayName}</span>
                  <PreloadImage onClick={() => handleDeleteSource(index)} src={del_icon_2 || ''} alt='del' style={{ width: '24px', height: '24px' }} />
                </div>
              ))}
            </div>


            {/* 共享用户 */}
            <div className={styles.sectionTitle}>允许访问媒体库</div>

            {/* 添加共享用户按钮 */}
            <div className={styles.addButton} onClick={handleAddUser}>
              <span>添加共享用户</span>
              <PreloadImage src={add_icon || ''} alt='add_icon' style={{ width: '24px', height: '24px' }} />
            </div>

            {/* 已选择的用户列表 */}
            {sharedUsers.map(user => (
              <div key={user.id} className={styles.userItem}>
                <div className={styles.userInfo}>
                  <Avatar src={user.avatar} className={styles.userAvatar} />
                  <div className={styles.userDetails}>
                    <span className={styles.userName}>{user.name}</span>
                    <span className={styles.userRole}>{user.role}</span>
                  </div>
                </div>
                <PreloadImage src={del_icon} alt='minus' onClick={() => handleDeleteUser(user.id)} />
              </div>
            ))}

            {/* 确认按钮 */}
            <div className={styles.confirmButtonContainer}>
              <button
                className={styles.confirmButton}
                onClick={handleSubmit}
                disabled={isSubmitting || isEditing}
              >
                {isSubmitting ? '创建中...' :
                  isEditing ? '保存中...' :
                    isEdit ? '保存' : '确定'}
              </button>
            </div>

          </div>

          {/* 文件选择器弹窗 */}
          <FileSelector
            visible={fileSelectorVisible}
            onClose={() => setFileSelectorVisible(false)}
            onSelect={handleSelectPath}
          />

          {/* 用户选择器弹窗 */}
          <UserSelector
            visible={userSelectorVisible}
            onClose={() => setUserSelectorVisible(false)}
            onConfirm={handleSelectUsers}
            selectedUserIds={sharedUsers.map(user => user.id)}
            users={allUsers}
          />
        </Route>
      </div>
    </>
  );
};

export default CreateLibrary;
