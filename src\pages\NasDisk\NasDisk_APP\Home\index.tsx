import  {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  Badge,
  Button,
  CapsuleTabs,
  Popover,
  Popup,
  Space,
  CheckList,
  Checkbox,
} from "antd-mobile";
import { useHistory, useLocation, useRouteMatch } from "react-router-dom";
import FileList, { FileItem, FileListRef } from "../components/FileList";
import ScheduledDownload from "../ScheduledDownload";
import styles from "./index.module.scss";
import NavigatorBar from "@/components/NavBar";
import { PreloadImage } from "@/components/Image";
import noFileImg from "@/Resources/nasDiskImg/no-file.png";
import addIcon from "@/Resources/camMgmtImg/add.png";
import more from "@/Resources/camMgmtImg/more.png";
import more_dark from "@/Resources/camMgmtImg/more-dark.png";
import { useTheme } from "@/utils/themeDetector";
import close from "@/Resources/icon/close.png";
import close_dark from "@/Resources/icon/close_white.png";
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import FloatPanel from "@/components/FloatPanel";
import { CheckListValue } from "antd-mobile/es/components/check-list";
import { getBaiduNetdiskFileList, BaiduFileItem } from "@/api/nasDisk";
import { useRequest } from "ahooks";
import { exitWebClient } from "@/api/cameraPlayer";
import { useUser } from "@/utils/UserContext";
import SynchronizationTab from "@/pages/NasDisk/NasDisk_APP/SynchronizationTab";

type LocationState = /*unresolved*/ any;
const NasDisk = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();
  const { path } = useRouteMatch();
  const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};
  const [activeTab, setActiveTab] = useState("diskfiles");
  const [visible, setVisible] = useState(false);
  const [showSortPanel, setShowSortPanel] = useState(false);
  const location = useLocation<LocationState>();
  const isVipFromState = location.state?.isVip;
  const tabFromState = location.state?.activeTab;
  const newTaskCount = location.state?.newTaskCount || 0;
  const [isEditing, setIsEditing] = useState(false);
  const [taskCount, setTaskCount] = useState(0);
  const [sortType, setSortType] = useState(0);
  const [sortOrder, setSortOrder] = useState(0);
  const [currentPath, setCurrentPath] = useState("/");
  const [fileData, setFileData] = useState<FileItem[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  // 添加跟踪自动下载任务状态
  const [hasDownloadTasks, setHasDownloadTasks] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  // 添加跟踪自动上传任务状态
  const [hasSyncTasks, setHasSyncTasks] = useState(false);
  const [isSyncEditMode, setIsSyncEditMode] = useState(false);

  // 动态生成Popover.Menu的actions
  const menuActions = useMemo(() => {
    const baseActions = [];
    // 根据不同的标签页和任务状态添加不同的选项
    if (activeTab === "diskfiles") {
      // 网盘文件标签下显示所有选项
      baseActions.push(
        { key: "choice", text: "选择" },
        { key: "filesort", text: "排序" },
        { key: "members", text: "我的" }
      );
    } else if (activeTab === "download" && hasDownloadTasks) {
      // 自动下载标签下，只有在有任务时才显示选择选项
      baseActions.push(
        { key: "choice", text: "选择" },
        { key: "members", text: "我的" }
      );
    } else if (activeTab === "synchronization" && hasSyncTasks) {
      // 自动上传标签下，只有在有任务时才显示选择选项
      baseActions.push(
        { key: "choice", text: "选择" },
        { key: "members", text: "我的" }
      );
    } else {
      // 其他情况只显示"我的"选项
      baseActions.push(
        { key: "members", text: "我的" }
      );
    }
    return baseActions;
  }, [activeTab, hasDownloadTasks, hasSyncTasks]);


  // 添加面包屑导航状态
  const [breadcrumbs, setBreadcrumbs] = useState<
    { label: string; path: string }[]
  >([{ label: "百度网盘", path: "/" }]);

  // 文件列表组件引用
  const fileListRef = useRef<FileListRef>(null);

  useEffect(() => {
    if (tabFromState) {
      setActiveTab(tabFromState === "autoDownload" ? "download" : tabFromState);
    } else if (isVipFromState) {
      setActiveTab("synchronization");
    }

    if (newTaskCount > 0) {
      setTaskCount((prev) => prev + newTaskCount);
      history.replace({
        pathname: location.pathname,
        state: { ...location.state, newTaskCount: 0 },
      });
    }
  }, [
    tabFromState,
    isVipFromState,
    newTaskCount,
    history,
    location.pathname,
    location.state,
  ]);

  // 监听标签页变化，确保切换时重置编辑状态
  useEffect(() => {
    // 重置编辑状态
    setIsEditing(false);
    setIsEditMode(false);
    setIsSyncEditMode(false);
    
  }, [activeTab, history, location.pathname, location.state]);

  // 处理标签页切换
  const showTab = (key: string) => {
    // 切换标签页时重置编辑状态
    if (key !== activeTab) {
      setIsEditing(false);
      setIsEditMode(false);
      setIsSyncEditMode(false);
    }
    setActiveTab(key);
  };

  const goToMenu = (path1: string) => {
    if (path1 === "members") {
      history.push(`${path}/${path1}`);
    } else if (path1 === "choice") {
      // 只有在网盘文件标签或者自动下载标签(有任务)或者自动上传标签(有任务)的情况下才能进入选择模式
      if (activeTab === "diskfiles") {
        setFileListEditing(true);
      }else if(activeTab === "download" && hasDownloadTasks){
        setIsEditMode(true)
      }else if(activeTab === "synchronization" && hasSyncTasks){
        setIsSyncEditMode(true)
      }
    } else if (path1 === "filesort" && activeTab === "diskfiles") {
      // 确保只在网盘文件标签下才能打开排序面板
      setShowSortPanel(true);
    }
  };

  // 处理文件夹导航
  const navigateToFolder = (path: string, folderName?: string) => {
    setCurrentPath(path);

    // 更新面包屑
    if (folderName) {
      // 如果直接点击文件夹，添加新的面包屑项
      setBreadcrumbs((prev) => [...prev, { label: folderName, path }]);
    }
  };

  // 处理点击事件
  const handleItemClick = (item: FileItem) => {
    if (item.type === "folder") {
      // 构建新路径，确保格式正确
      const newPath =
        currentPath === "/" ? `/${item.name}` : `${currentPath}/${item.name}`;

      // 更新路径状态，这会触发useEffect加载新的文件列表
      navigateToFolder(newPath, item.name);
    } else {
      console.log("点击了文件:", item.name);
      // 这里可以添加文件预览等功能
    }
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (
    item: { label: string; path: string },
    index: number
  ) => {
    // 设置当前路径
    setCurrentPath(item.path);

    // 更新面包屑，截取到点击的位置
    setBreadcrumbs((prev) => prev.slice(0, index + 1));
  };

  const handleDownload = (selectedIds: number[]) => {
    setTaskCount((prev) => prev + selectedIds.length);
  };

  const handleAddClick = () => {
    history.push({
      pathname: `${path}/fileUpload`,
    });
  };

  // 处理选择变化
  const handleSelectionChange = (ids: number[]) => {
    setSelectedIds(ids);
  };

  // 处理全选/取消全选
  const handleToggleSelectAll = () => {
    if (selectedIds.length === fileData.length) {
      // 取消全选
      setSelectedIds([]);
    } else {
      // 全选
      setSelectedIds(fileData.map((file) => file.id));
    }
  };

  const right = (
    <div style={{ fontSize: 24 }}>
      <Space style={{ "--gap": "10px" }}>
        <div
          className={styles.taskContent}
          onClick={() => history.push("/baiduNetdisk_app/TaskManager")}
        >
          <Badge content={taskCount > 0 ? Badge.dot : ""} bordered>
            任务
          </Badge>
        </div>

        <Popover.Menu
          actions={menuActions}
          placement="bottom-start"
          onAction={(node) => goToMenu(node.key as string)}
          trigger="click"
        >
          <PreloadImage src={isDarkMode ? more_dark : more} alt="more" />
        </Popover.Menu>
      </Space>
    </div>
  );

  // 处理返回
  const handleBack = () => {
    // 如果处于编辑态，先退出编辑态
    if (activeTab === "diskfiles" && isEditing) {
      setFileListEditing(false);

      // 如果是从下载位置选择页面返回后的编辑态，需要清除location.state中的编辑状态
      if (location.state?.downloadPath) {
        history.replace({
          pathname: location.pathname,
          state: {
            ...location.state,
            isEditing: false,
            downloadCompleted: false, // 清除下载完成标志，以便用户可以再次进入编辑态
          },
        });
      }
      return;
    }

    // 如果在自动下载标签页且处于编辑模式，退出编辑模式
    if (activeTab === "download" && isEditMode) {
      setIsEditMode(false);
      return;
    }

    // 如果在自动上传标签页且处于编辑模式，退出编辑模式
    if (activeTab === "synchronization" && isSyncEditMode) {
      setIsSyncEditMode(false);
      return;
    }

    // 检查是否在根目录
    if (currentPath !== "/" && activeTab === "diskfiles") {
      // 获取上级目录路径
      const pathParts = currentPath.split("/").filter(Boolean);
      pathParts.pop(); // 移除最后一个目录名
      const parentPath =
        pathParts.length === 0 ? "/" : `/${pathParts.join("/")}`;

      // 更新路径并重新加载文件列表
      setCurrentPath(parentPath);
      // 更新面包屑，移除最后一项
      setBreadcrumbs((prev) => prev.slice(0, prev.length - 1));
      return;
    }

    // 已在根目录，返回上一页面
    exitWebClient().catch((error) => {
      console.error("退出Web客户端失败:", error);
    });
  };

  const handleEditStateChange = (editing: boolean) => {
    setIsEditing(editing);
  };

  const setFileListEditing = (editing: boolean) => {
    if (fileListRef.current) {
      fileListRef.current.setIsEditing(editing);
      // 如果退出编辑态，确保重置选中状态
      if (!editing && fileListRef.current.resetSelection) {
        fileListRef.current.resetSelection();
      }
    } else {
      setIsEditing(editing);
    }
  };

  // 处理排序类型变化
  const handleSortTypeChange = (val: CheckListValue[]) => {
    setSortType(val[0] as number);
    // 关闭排序面板
    setShowSortPanel(false);
  };

  // 处理排序顺序变化
  const handleSortOrderChange = (val: CheckListValue[]) => {
    setSortOrder(val[0] as number);
    // 关闭排序面板
    setShowSortPanel(false);
  };

  // 使用 useRequest 获取文件列表
  const { run: fetchFileList } = useRequest(
    (params: { path: string; order: string; desc: number }) => {
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: encodeURIComponent(params.path),
        order: params.order, // 添加排序字段参数
        desc: params.desc, // 添加排序顺序参数
        web: 1, // 添加web参数，确保返回缩略图数据
      }).catch((error) => {
        console.error("获取文件列表失败，使用模拟数据", error);
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const files: FileItem[] = response.list.map(
            (item: BaiduFileItem) => ({
              id: item.fs_id,
              name: item.server_filename,
              type: item.isdir === 1 ? "folder" : "file",
              time: new Date(item.server_mtime * 1000).toLocaleString(),
              icon:
                item.thumbs && typeof item.thumbs === "object"
                  ? item.thumbs.icon || item.thumbs.url1 || ""
                  : "https://example.com/file-icon.png",
              size: item.size || 0,
              children: item.dir_empty === 0 ? [] : undefined, // 如果目录不为空，预设一个空数组
              category: item.category, // 添加分类属性
              thumbs: item.thumbs, // 直接传递thumbs对象
            })
          );

          setFileData(files);
        } else {
          console.error("获取文件列表失败:", response);
        }
      },
      onError: (error) => {
        console.error("加载文件列表出错:", error);
      },
    }
  );

  // 加载文件列表
  const loadFileList = useCallback(() => {
    // 获取对应的排序字段
    const orderField =
      sortType === 0 ? "name" : sortType === 1 ? "size" : "time";

    // 使用 useRequest 发起请求
    fetchFileList({
      path: currentPath,
      order: orderField, // 排序字段：name(文件名)、size(大小)、time(时间)
      desc: sortOrder, // 排序顺序：0(升序)、1(降序)
    });
  }, [currentPath, sortType, sortOrder, fetchFileList]);

  // 当排序条件或当前路径变化时重新加载文件列表
  useEffect(() => {
    loadFileList();
  }, [loadFileList]);

  // 判断是否需要显示右侧按钮
  const shouldShowRight = useMemo(() => {
    // 在网盘文件标签下，根据isEditing决定是否显示right
    if (activeTab === "diskfiles") {
      return !isEditing;
    }
    // 在自动下载标签下，根据isEditMode决定是否显示right
    if (activeTab === "download") {
      return !isEditMode;
    }
    // 在自动上传标签下，根据isSyncEditMode决定是否显示right
    if (activeTab === "synchronization") {
      return !isSyncEditMode;
    }
    // 其他标签页默认显示right
    return true;
  }, [activeTab, isEditing, isEditMode, isSyncEditMode]);

  // 根据当前标签和编辑状态决定标题内容
  const getTitle = useMemo(() => {
    if (activeTab === "diskfiles") {
      return isEditing ? "选择要下载的文件" : "百度网盘";
    } else if (activeTab === "download") {
      return isEditMode ? "删除任务" : "百度网盘";
    } else if (activeTab === "synchronization") {
      return isSyncEditMode ? "删除任务" : "百度网盘";
    }
    return "百度网盘";
  }, [activeTab, isEditing, isEditMode, isSyncEditMode]);

  return (
    <>
      <NavigatorBar
        backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
        onBack={handleBack}
        right={shouldShowRight ? right : null}
      />
      <div className={styles.title}>{getTitle}</div>
      <div className={styles.fileTab}>
        <CapsuleTabs onChange={showTab} activeKey={activeTab}>
          <CapsuleTabs.Tab title="网盘文件" key="diskfiles" />
          <CapsuleTabs.Tab title="自动下载" key="download" />
          <CapsuleTabs.Tab
            title={<Badge content="VIP专享">自动上传</Badge>}
            key="synchronization"
          />
        </CapsuleTabs>
      </div>

      {/* 面包屑导航将在FileList组件内部处理 */}

      {activeTab === "diskfiles" && fileData.length > 0 && (
        <>
          {/* 面包屑导航与全选按钮在同一行，移到列表外面 */}
          <div className={styles.breadcrumbRow}>
            <div className={styles.breadcrumbContainer}>
              {breadcrumbs.length > 3 ? (
                <div className={styles.breadcrumbContent}>
                  <div className={styles.breadcrumbItem}>...</div>
                  {breadcrumbs.slice(-2).map((item, index) => (
                    <div
                      className={styles.breadcrumbContent}
                      key={`${item.path}_${index}`}
                    >
                      <div className={styles.breadcrumbNextContainer}>
                        <PreloadImage
                          className={styles.breadcrumbNextImg}
                          src={isDarkMode ? next_dark : next}
                          alt="arrow"
                        />
                      </div>
                      <div
                        onClick={() =>
                          handleBreadcrumbClick(
                            item,
                            breadcrumbs.length - 2 + index
                          )
                        }
                        className={`${styles.breadcrumbItem} ${
                          index === 1 ? styles.breadcrumbItemActive : ""
                        }`}
                      >
                        {item.label}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                breadcrumbs.map((item, index) => (
                  <div
                    className={styles.breadcrumbContent}
                    key={`${item.path}_${index}`}
                  >
                    {index !== 0 && (
                      <div className={styles.breadcrumbNextContainer}>
                        <PreloadImage
                          className={styles.breadcrumbNextImg}
                          src={isDarkMode ? next_dark : next}
                          alt="arrow"
                        />
                      </div>
                    )}
                    <div
                      onClick={() => handleBreadcrumbClick(item, index)}
                      className={`${styles.breadcrumbItem} ${
                        index === breadcrumbs.length - 1
                          ? styles.breadcrumbItemActive
                          : ""
                      }`}
                    >
                      {item.label}
                    </div>
                  </div>
                ))
              )}
            </div>
            {/* 编辑状态下显示全选按钮 */}
            {isEditing && (
              <div className={styles.selectAllButton}>
                <Checkbox
                  checked={
                    selectedIds.length === fileData.length &&
                    fileData.length > 0
                  }
                  onChange={handleToggleSelectAll}
                />
              </div>
            )}
          </div>

          {/* 文件列表，独立滚动 */}
          <div className={styles.fileListWrapper}>
            <FileList
              data={fileData}
              onItemClick={handleItemClick}
              onDownload={handleDownload}
              onAddClick={handleAddClick}
              title={
                currentPath === "/"
                  ? "百度网盘"
                  : currentPath.split("/").filter(Boolean).pop() || "百度网盘"
              }
              onEditStateChange={handleEditStateChange}
              ref={fileListRef}
              loading={false}
              isVip={nas_vip === 1}
              onBackClick={handleBack}
              // 新增: 传递选中ID列表和选择变化回调
              externalSelectedIds={selectedIds}
              onSelectionChange={handleSelectionChange}
              currentPath={currentPath}
            />
          </div>
        </>
      )}

      {activeTab === "diskfiles" && fileData.length === 0 && (
        <>
          <div className={styles.breadcrumbRow}>
            <div className={styles.breadcrumbContainer}>
              {breadcrumbs.length > 3 ? (
                <div className={styles.breadcrumbContent}>
                  <div className={styles.breadcrumbItem}>...</div>
                  {breadcrumbs.slice(-2).map((item, index) => (
                    <div
                      className={styles.breadcrumbContent}
                      key={`${item.path}_${index}`}
                    >
                      <div className={styles.breadcrumbNextContainer}>
                        <PreloadImage
                          className={styles.breadcrumbNextImg}
                          src={isDarkMode ? next_dark : next}
                          alt="arrow"
                        />
                      </div>
                      <div
                        onClick={() =>
                          handleBreadcrumbClick(
                            item,
                            breadcrumbs.length - 2 + index
                          )
                        }
                        className={`${styles.breadcrumbItem} ${
                          index === 1 ? styles.breadcrumbItemActive : ""
                        }`}
                      >
                        {item.label}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                breadcrumbs.map((item, index) => (
                  <div
                    className={styles.breadcrumbContent}
                    key={`${item.path}_${index}`}
                  >
                    {index !== 0 && (
                      <div className={styles.breadcrumbNextContainer}>
                        <PreloadImage
                          className={styles.breadcrumbNextImg}
                          src={isDarkMode ? next_dark : next}
                          alt="arrow"
                        />
                      </div>
                    )}
                    <div
                      onClick={() => handleBreadcrumbClick(item, index)}
                      className={`${styles.breadcrumbItem} ${
                        index === breadcrumbs.length - 1
                          ? styles.breadcrumbItemActive
                          : ""
                      }`}
                    >
                      {item.label}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className={styles.fileListWrapper}>
            <div className={styles.emptyContainer}>
              <PreloadImage
                src={noFileImg}
                alt="no-file"
                className={styles.emptyImage}
              />
              <div className={styles.emptyText}>文件夹为空</div>
            </div>
          </div>
        </>
      )}

      {activeTab === "download" && (
        <ScheduledDownload
          isVip={nas_vip === 1}
          onTasksChange={setHasDownloadTasks}
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
        />
      )}

                    {activeTab === "synchronization" && (
            <SynchronizationTab isVip={nas_vip === 1} onTasksChange={setHasSyncTasks} isEditMode={isSyncEditMode} setIsEditMode={setIsSyncEditMode} />
          )}

      {activeTab === "diskfiles" && !isEditing && (
        <div className={styles.addButton} onClick={handleAddClick}>
          <PreloadImage src={addIcon} alt="add" className={styles.addIcon} />
        </div>
      )}

      <Popup
        visible={visible}
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
        bodyStyle={{
          height: "25vh",
          margin: "0 auto",
          backgroundColor: "#fff",
          width: "90%",
          marginBottom: "10%",
          marginLeft: "5%",
          borderRadius: "50px",
        }}
      >
        <div className={styles.unbindContent}>
          <h1>解绑账号</h1>
          <p>
            解除绑定后，将无法使用网盘相关服务，且已配置的自动同步任务和下载中任务也将清空。确定解绑?
          </p>
          <div className={styles.unbindButton}>
            <Button
              onClick={() => setVisible(false)}
              style={{ backgroundColor: "#ccc", color: "black" }}
            >
              取消
            </Button>
            <Button onClick={() => setVisible(false)}>确定</Button>
          </div>
        </div>
      </Popup>

      {/* 排序面板 */}
      <FloatPanel
        showFloatPanel={showSortPanel}
        setShowFloatPanel={setShowSortPanel}
      >
        <div className={styles.filter_float_panel_container}>
          <div className={styles.filter_float_panel_navBar}>
            <PreloadImage
              src={isDarkMode ? close_dark : close}
              alt="close"
              onClick={() => setShowSortPanel(false)}
            />
            <span>排序设置</span>
          </div>
          <div className={styles.filter_float_panel_content}>
            <span className={styles.filter_float_panel_content_list_title}>
              筛选
            </span>
            <div
              className={styles.filter_float_panel_content_check_list_container}
            >
              <CheckList value={[sortType]} onChange={handleSortTypeChange}>
                <CheckList.Item
                  style={{
                    color: sortType === 0 ? "var(--primary-color)" : "",
                  }}
                  value={0}
                >
                  文件名称
                </CheckList.Item>
                <CheckList.Item
                  style={{
                    color: sortType === 1 ? "var(--primary-color)" : "",
                  }}
                  value={1}
                >
                  文件大小
                </CheckList.Item>
                <CheckList.Item
                  style={{
                    color: sortType === 2 ? "var(--primary-color)" : "",
                  }}
                  value={2}
                >
                  修改时间
                </CheckList.Item>
              </CheckList>
            </div>
            <span className={styles.filter_float_panel_content_list_title}>
              排序
            </span>
            <div
              className={styles.filter_float_panel_content_check_list_container}
            >
              <CheckList value={[sortOrder]} onChange={handleSortOrderChange}>
                <CheckList.Item
                  style={{
                    color: sortOrder === 0 ? "var(--primary-color)" : "",
                  }}
                  value={0}
                >
                  升序
                </CheckList.Item>
                <CheckList.Item
                  style={{
                    color: sortOrder === 1 ? "var(--primary-color)" : "",
                  }}
                  value={1}
                >
                  降序
                </CheckList.Item>
              </CheckList>
            </div>
          </div>
        </div>
      </FloatPanel>
    </>
  );
};

export default NasDisk;
