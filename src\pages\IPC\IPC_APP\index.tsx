import { Route, useRouteMatch } from "react-router-dom";
import { useRequest } from "ahooks";
import IPCHome from "./Home";
import NotAdded from "./NotAdded";
import styles from "./index.module.scss";
import { useState } from "react";
import { listRecordCamera, CameraInfo } from "@/api/ipc";
import testCard from "@/Resources/camMgmtImg/test-card.png";

export default function CameraManagement(props: {
  children?: React.ReactNode;
}) {
  const [hasCameras, setHasCameras] = useState(false);
  const [cameraList, setCameraList] = useState<CameraInfo[]>([
    {
      did: "123456789",
      name: "客厅",
      model: "小米智能摄像机3 云台版",
      record_enabled: true,
      ip: "**************",
      mac: "20:25:04:23:14:32",
      record_period: 1159,
      used_space: 4918,
      space_limit: 5120,
      key_frame: [
        {
          lens_id: 0,
          frame: testCard,
          psm: true
        },
        {
          lens_id: 1,
          frame: testCard,
          psm: false
        },
      ],
      isOnline: true
    },
    {
      did: "123456788",
      name: "花园",
      model: "小米室外摄像机CW500双摄版",
      record_enabled: true,
      ip: "**************",
      mac: "20:25:04:23:14:32",
      record_period: 1159,
      used_space: 4918,
      space_limit: 5120,
      key_frame: [
        {
          lens_id: 0,
          frame: testCard,
          psm: false
        },
        {
          lens_id: 1,
          frame: testCard,
          psm: false
        },
      ],
      isOnline: false
    },
    {
      did: "1037052118",
      name: "婴儿房",
      model: "小米智能摄像机 母婴看护版",
      record_enabled: true,
      ip: "**************",
      mac: "20:25:04:23:14:32",
      record_period: 1159,
      used_space: 4918,
      space_limit: 5120,
      key_frame: [
        {
          lens_id: 0,
          frame: testCard,
          psm: false // 是否休眠
        },
        {
          lens_id: 1,
          frame: testCard,
          psm: false
        },
      ],
      isOnline: true
    },
  ]);
  const { path } = useRouteMatch();

  // const { loading } = 
  useRequest(
    () => listRecordCamera({ did: [] }),
    {
      manual: false,
      onSuccess: (res) => {
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
      },
      onError: (err) => {
        console.error("摄像头列表获取失败:", err);
        setCameraList([]);
        setHasCameras(false);
      },
    }
  );

  // 提供刷新摄像头列表的方法
  const refreshCameraList = () => {
    // run({});
  };

  // if (loading)
  //   return (
  //     <div className={styles.loadingContainer}>
  //       <Loading color="primary" />
  //       <span className={styles.loadingText}>加载中...</span>
  //     </div>
  //   );

  return (
    <div id="cameraManagementContainer" className={styles.cameraManagementContainer}>
      <div className={styles.top}></div>
      <div className={styles.content}>
        {props.children}
        <Route exact path={path}>
          {!hasCameras ? (
            <IPCHome
              cameraList={cameraList}
              refreshCameraList={refreshCameraList}
            />
          ) : (
            <NotAdded />
          )}
        </Route>
      </div>
    </div>
  );
}
