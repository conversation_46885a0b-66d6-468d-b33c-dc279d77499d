import request from "@/request";
export interface ApiResponse<T> {
  code: number;
  result: string;
  data: T;
}

export interface KeyFrame {
  lens_id: number;
  frame: string;
  psm: boolean // 是否休眠
}

export interface CameraInfo {
  did: string;
  name: string;
  model: string;
  record_enabled: boolean;
  ip: string;
  mac: string;
  record_period: number;
  used_space: number;
  space_limit: number;
  key_frame: KeyFrame[];
  isOnline: boolean
}

export interface BasicCameraInfo {
  did: string;
  name: string;
  model: string;
  ip: string;
  mac: string;
}

export interface RecordCameraData {
  camera: CameraInfo[];
}

export interface AllCameraData {
  camera: BasicCameraInfo[];
}

export interface RenameDeviceParams {
  name: string;
  did: string;
}

export interface RenameDeviceResponse {
  result: boolean;
  code: number;
  message: string;
}

// 录制计划项
export interface ScheduleItem {
  enabled: boolean;
  start: string;
  end: string;
  repeat_policy: string;
  customized?: string[];
}

// 录制计划配置
export interface RecordSchedule {
  type: string;
  schedule?: ScheduleItem[];
}

// 事件触发器配置
export interface EventTrigger {
  name: string;
  enabled: boolean;
  sensitivity: number;
}

// 录制配置
export interface RecordConfig {
  record_enabled?: boolean;
  backup?: boolean;
  path?: string;
  retention_period?: number;
  record_mode?: string;
  record_resolution?: string;
  space_limit?: number;
  space_limit_policy?: string;
  record_schedule?: RecordSchedule;
  event_source?: string;
  event_trigger?: EventTrigger[];
  record_bf_event?: number;
  record_af_event?: number;
  ntp_source?: string;
}

// 设置IPC录制参数请求接口
export interface SetupRecordCameraParams {
  camera: string[];
  config: RecordConfig;
}

export interface StandardResponse {
  code: number;
  result: string;
}

export interface CleanRecordParams {
  camera: string[];
}

export interface SetFacialRecognitionParams {
  enabled: boolean;
}

export interface FacialInfo {
  uuid: string;
  name: string;
  profile_pic: string;
  picture_dir: string;
  video_dir: string;
}

export interface FacialInfoData {
  info: FacialInfo[];
}

export interface SetFacialInfoParams {
  uuid: string;
  name?: string;
  profile_pic?: string;
  delete?: boolean
}

export interface GetFacialPhotoParams {
  uuid: string;
}

export interface GetFacialPhotoData {
  photo: string[];
}

export interface AddFacialPhotoParams {
  uuid: string;
  photo: string[];
}

export interface GetRecordConfigParams {
  camera: string;
}

export interface RecordConfigData {
  record_enabled: boolean;
  backup: boolean;
  path: string;
  retention_period: number;
  record_mode: string;
  record_resolution: string;
  space_limit: number;
  space_limit_policy: string;
  record_schedule: RecordSchedule;
  event_source: string;
  event_trigger: EventTrigger[];
  record_bf_event: number;
  record_af_event: number;
  ntp_source: string;
}
export interface DelFacialVideoParams {
  uuid: string;
  video: string[];
}
export interface FacialRecognitionStatus {
  enabled: boolean;
}

export interface SupportedCameraModel {
  model: string;
  model_name: string;
  icon: string;
}

export interface SupportedCameraListData {
  camera: SupportedCameraModel[];
}

export interface CameraEventData {
  page: {
    size: number,
    token: string
  }
  videos: {
    camera_lens: string,
    event_name: string,
    time: string,
    media_duration: number,
    file: string,
    create_time: string
    face_info: {
      uuid: string;
      name: string;
      profile_pic: string;
    }[]
    cover_file: string,
    // cover: string,
    // face_uuid: string,
    // encode: string,
    // size: string,
  }[]
}

export interface StartRecordParams {
  camera_lens: string;
  id: number
}

export interface StopRecordData {
  camera_lens: string;
  event_name: string[];
  time: string;
  media_duration: number;
  cover: string;
  file: string;
}

// 获取人脸关联视频请求参数
export interface GetFacialVideoParams {
  page: {
    size: number;
    token: string;
  };
  uuid: string;
}

// 获取人脸关联视频响应数据
export interface FacialVideoData {
  page: {
    size: number;
    token: string;
  };
  videos: {
    time: string;
    media_duration: number;
    face_file: string;
    cover_file: string;
    file: string;
  }[];
}

export const startLiveWithCamera = (camera: string[], time?: string): Promise<ApiResponse<{
  [key: string]: {
    hls_file: string,
    key_frame: KeyFrame[]
  }
}>> => request.post('/ipcmgr/start_hls', { camera_lens: camera, time: time }); // 开启直播
export const stopLiveWithCamera = (camera: string[]) => request.post('/ipcmgr/stop_hls', camera); // 结束直播

// 获取已添加录制的IPC列表
export const listRecordCamera = (params: {
  did?: string[]
}): Promise<ApiResponse<RecordCameraData>> =>
  request.post('/ipcmgr/list_record_camera', params);

// 获取用户名下IPC列表
export const listAllCamera = (): Promise<ApiResponse<AllCameraData>> =>
  request.post('/ipcmgr/list_all_camera', {});

// 重命名设备
export const renameDevice = (params: RenameDeviceParams): Promise<RenameDeviceResponse> =>
  request.post('/ipcmgr/set_ipc_name', params);

// 设置IPC本地录制
export const setupRecordCamera = (params: SetupRecordCameraParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/setup_record_camera', params);

// 清空IPC存档记录
export const cleanRecord = (params: CleanRecordParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/clean_record', params);

// 移除摄像头
export const removeRecordCamera = (params: CleanRecordParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/remove_record_camera', params);

// 设置人脸识别
export const setFacialRecognition = (params: SetFacialRecognitionParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/set_facial_recognition', params);

// 获取人脸识别数据
export const getFacialInfo = (): Promise<ApiResponse<FacialInfoData>> =>
  request.post('/ipcmgr/get_facial_info', {});

// 获取人脸相关照片
export const getFacialPhoto = (params: GetFacialPhotoParams): Promise<ApiResponse<GetFacialPhotoData>> =>
  request.post('/ipcmgr/get_facial_photo', params);

// 添加关联照片到人脸
export const addFacialPhoto = (params: AddFacialPhotoParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/add_facial_photo', params);

// 删除人脸关联照片
export const delFacialPhoto = (params: AddFacialPhotoParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/del_facial_photo', params);

// 修改人脸信息
export const setFacialInfo = (params: SetFacialInfoParams): Promise<StandardResponse> =>
  request.post('/ipcmgr/set_facial_info', params);

// 获取IPC本地录制配置
export const getRecordConfig = (params: GetRecordConfigParams): Promise<ApiResponse<RecordConfigData>> => request.post('/ipcmgr/get_record_config', params);
export const getCameraRecordList = () => request.post('/ipcmgr/list_record_camera'); // 获取已添加录制的IPC列表
export const getVideoRecord = (params: any): Promise<ApiResponse<CameraEventData>> => request.post('/ipcmgr/get_record_video', params); // 获取事件片段列表

// 删除人脸关联视频
export const delFacialVideo = (
  params: DelFacialVideoParams
): Promise<StandardResponse> => request.post("/ipcmgr/del_facial_video", params);

// 获取人脸识别设置
export const getFacialRecognition = (): Promise<ApiResponse<FacialRecognitionStatus>> =>
  request.post('/ipcmgr/get_facial_recognition', {});

// 获取支持的摄像头型号列表
export const getSupportCameraList = (): Promise<ApiResponse<SupportedCameraListData>> =>
  request.post('/ipcmgr/get_support_list', {});

// 开始视频转存
export const startRecord = (params: StartRecordParams): Promise<ApiResponse<{ id: number }>> => request.post('/ipcmgr/start_record', params);

// 结束视频转存
export const stopRecord = (params: StartRecordParams): Promise<ApiResponse<StopRecordData>> => request.post('/ipcmgr/stop_record', params);

// 获取人脸关联视频
export const getFacialVideo = (
  params: GetFacialVideoParams
): Promise<ApiResponse<FacialVideoData>> =>
  request.post("/ipcmgr/get_facial_video", params);
