import React, { useState, useCallback, useEffect } from 'react';
import { Popup, List, Button, Checkbox, Avatar, Loading, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import { useTheme } from "@/utils/themeDetector";

import backIcon_light from '@/Resources/icon/backIcon_light.png';
import backIcon_dark from '@/Resources/icon/backIcon_dark.png';
import { getUserList, UserInfo } from '@/api/fatWallPlayer';
import { PreloadImage } from '@/components/Image';

export interface User {
  id: string;
  name: string;
  avatar: string;
  role: string;
  uid?: string;
  permission?: 'admin' | 'user';
  group?: string[];
  status?: 'active' | 'inactive';
}

interface UserSelectorProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedUsers: User[]) => void;
  selectedUserIds?: string[]; // 已选择的用户ID列表
  users?: User[]; // 外部传入的用户列表
  title?: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  visible,
  onClose,
  onConfirm,
  selectedUserIds = [],
  users: externalUsers = [],
  title = '添加用户'
}) => {
  // 已选择的用户列表
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const { isDarkMode } = useTheme();
  // 用户列表
  const [users, setUsers] = useState<User[]>([]);
  const [userListLoading, setUserListLoading] = useState<boolean>(false);

  // 默认用户数据
  const getDefaultUsers = useCallback((): User[] => [], []);

  // 获取用户列表
  const fetchUserList = useCallback(async () => {
    try {
      setUserListLoading(true);
      
      await getUserList((response) => {
        if (response.code === 0 && response.data) {
          // 转换API返回的用户数据为本地格式
          const userList: User[] = response.data.list.map((user: UserInfo) => ({
            id: user.uid,
            name: user.nickname,
            avatar: user.headImg , // 使用默认头像，后续可以根据用户信息动态设置
            role: user.permission === 'admin' ? '管理员' : '普通用户',
            uid: user.uid,
            permission: user.permission,
            group: user.group,
            status: user.status
          }));
          setUsers(userList);
          console.log('获取用户列表成功:', userList);
        } else {
          // API返回数据格式不正确时使用默认数据
          console.warn('API返回数据格式不正确，使用默认数据');
          setUsers(getDefaultUsers());
        }
      });
    } catch (error) {
      console.error('获取用户列表失败：', error);
      // 接口失败时使用默认数据
      setUsers(getDefaultUsers());
      
      Toast.show({
        content: '网络异常，已加载默认数据',
        position: 'bottom',
        duration: 2000,
      });
    } finally {
      setUserListLoading(false);
    }
  }, [getDefaultUsers]);

  // 初始化用户列表
  useEffect(() => {
    if (visible) {
      if (externalUsers.length > 0) {
        // 如果有外部传入的用户列表，直接使用
        setUsers(externalUsers);
      } else {
        // 否则先设置默认数据，然后获取真实数据
        setUsers(getDefaultUsers());
        fetchUserList();
      }
    }
  }, [visible, externalUsers, fetchUserList, getDefaultUsers]);

  // 当用户列表加载完成后，根据selectedUserIds初始化已选择的用户
  useEffect(() => {
    if (users.length > 0 && selectedUserIds.length > 0) {
      const preSelectedUsers = users.filter(user => selectedUserIds.includes(user.id));
      setSelectedUsers(preSelectedUsers);
    }
  }, [users, selectedUserIds]);

  // 切换用户选择状态
  const toggleUserSelection = useCallback((user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(item => item.id === user.id);
      if (isSelected) {
        return prev.filter(item => item.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  }, []);
  
  // 确认选择
  const handleConfirm = useCallback(() => {
    onConfirm(selectedUsers);
    onClose();
    // 清空已选择用户
    setSelectedUsers([]);
  }, [selectedUsers, onConfirm, onClose]);

  // 关闭弹窗时重置状态
  const handleClose = useCallback(() => {
    setSelectedUsers([]);
    onClose();
  }, [onClose]);
  
  return (
    <Popup
      visible={visible}
      onMaskClick={handleClose}
      bodyStyle={{
        borderTopLeftRadius: '30px',
        borderTopRightRadius: '30px',
        height: '95%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header}>
          {/* <LeftOutline onClick={handleClose} /> */}
          <PreloadImage src={isDarkMode ? backIcon_dark : backIcon_light} alt='minus' onClick={handleClose} style={{width: '40px', height: '40px'}} />
          <span className={styles.title}>{title}</span>
          <div style={{ width: 24 }}></div>
        </div>
        
        {/* 加载状态 */}
        {userListLoading && (
          <div className={styles.loadingContainer}>
            <Loading />
            <span>加载中...</span>
          </div>
        )}
        
        {/* 用户列表 */}
        <div className={styles.userList}>
          <List 
          style={{'--border-inner': 'none','--border-bottom': 'none'}
           }>
            {users.map(user => (
              <List.Item
                key={user.id}
                prefix={
                    <div className={styles.avatar}>
                        <Avatar src={user.avatar} />
                    </div>
                }
                extra={
                  <Checkbox
                    checked={selectedUsers.some(item => item.id === user.id)}
                    // onChange={() => toggleUserSelection(user)}
                  />
                }
                onClick={() => toggleUserSelection(user)}
                arrowIcon={false}
                clickable={false}
              >
                <div className={styles.userName}>{user.name}</div>
                <div className={styles.userRole}>{user.role}</div>
              </List.Item>
            ))}
          </List>

          {!userListLoading && users.length === 0 && (
            <div className={styles.emptyState}>
              <span>暂无可选择的用户</span>
            </div>
          )}
        </div>
        
        
        {/* 底部确认按钮 */}
        <div className={styles.footer}>
          <Button
            className={styles.confirmButton}
            disabled={selectedUsers.length === 0}
            onClick={handleConfirm}
            color='primary'
            size='large'
          >
            确定
          </Button>
        </div>
      </div>
    </Popup>
  );
};

export default UserSelector; 