import React, { useState, useMemo, useEffect, useCallback } from "react";
import NavigatorBar from "@/components/NavBar";
import styles from "./index.module.scss";
import filterIcon from "@/Resources/camMgmtImg/videoEdit.png";
import filterDarkIcon from "@/Resources/camMgmtImg/videoEdit.png";
import dateSelect from '@/Resources/player/dateSelect.png';
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import { useHistory } from "react-router-dom";
import PopoverSelector from "@/components/PopoverSelector";
import DatePicker from "@/components/CameraPlayer/components/DatePicker/DatePicker";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useRequest } from "ahooks";
import { listRecordCamera, CameraInfo, getVideoRecord } from "@/api/ipc";
import request from "@/request";
import VideoEditModal from "./VideoEditModal";

// 视频项接口，基于getVideoRecord接口返回的数据结构
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string; // 时间戳
  media_duration: number;
  file: string;
  create_time: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  // 计算属性
  timeLabel?: string; // 显示的时间，如 "14:00"
  thumbnail?: string; // 缩略图
}

// 摄像机选项接口
interface CameraOption {
  label: string;
  value: string;
}

// 默认mock数据，符合getVideoRecord接口返回的数据结构
const mockVideoData: VideoItem[] = [
  {
    camera_lens: "12345678_1",
    event_name: "motion_detect",
    time: "1699776000", // 2023-11-12 14:00:00 的时间戳
    media_duration: 30,
    file: "/api/placeholder/video1.mp4",
    create_time: "2023-11-12T14:00:00Z",
    face_info: [],
    timeLabel: "14:00-14:00", // 30秒，实际应该是14:00-14:00
    thumbnail: "/api/placeholder/120/80",
  },
  {
    camera_lens: "12345678_1",
    event_name: "motion_detect",
    time: "1699772400", // 2023-11-12 13:00:00 的时间戳
    media_duration: 25,
    file: "/api/placeholder/video2.mp4",
    create_time: "2023-11-12T13:00:00Z",
    face_info: [],
    timeLabel: "13:00-13:00", // 25秒
    thumbnail: "/api/placeholder/120/80",
  },
  {
    camera_lens: "12345678_1",
    event_name: "human_detect",
    time: "1699768800", // 2023-11-12 12:00:00 的时间戳
    media_duration: 40,
    file: "/api/placeholder/video3.mp4",
    create_time: "2023-11-12T12:00:00Z",
    face_info: [],
    timeLabel: "12:00-12:00", // 40秒
    thumbnail: "/api/placeholder/120/80",
  },
  {
    camera_lens: "12345678_1",
    event_name: "motion_detect",
    time: "1699765200", // 2023-11-12 11:00:00 的时间戳
    media_duration: 35,
    file: "/api/placeholder/video4.mp4",
    create_time: "2023-11-12T11:00:00Z",
    face_info: [],
    timeLabel: "11:00-11:00", // 35秒
    thumbnail: "/api/placeholder/120/80",
  },
  {
    camera_lens: "12345678_1",
    event_name: "motion_detect",
    time: "1699761600", // 2023-11-12 10:00:00 的时间戳
    media_duration: 20,
    file: "/api/placeholder/video5.mp4",
    create_time: "2023-11-12T10:00:00Z",
    face_info: [],
    timeLabel: "10:00-10:00", // 20秒
    thumbnail: "/api/placeholder/120/80",
  },
  {
    camera_lens: "12345678_1",
    event_name: "human_detect",
    time: "1699758000", // 2023-11-12 09:00:00 的时间戳
    media_duration: 45,
    file: "/api/placeholder/video6.mp4",
    create_time: "2023-11-12T09:00:00Z",
    face_info: [],
    timeLabel: "09:00-09:00", // 45秒
    thumbnail: "/api/placeholder/120/80",
  },
];

// 默认摄像机选项数据（作为fallback）
const defaultCameraOptions: CameraOption[] = [
  { label: "摄像机01", value: "camera01" },
  { label: "摄像机02", value: "camera02" },
  { label: "摄像机03", value: "camera03" },
];

export default function VideoList() {
  const { isDarkMode } = useTheme();
  const history = useHistory();

  // 视频数据状态
  const [videoData, setVideoData] = useState<VideoItem[]>(mockVideoData);
  const [pageInfo, setPageInfo] = useState({ size: 20, token: "" });

  // 摄像机数据状态
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [cameraOptions, setCameraOptions] = useState<CameraOption[]>(defaultCameraOptions);

  // 摄像机选择器状态
  const [selectedCamera, setSelectedCamera] = useState<string>("camera01");
  const [cameraPopoverVisible, setCameraPopoverVisible] = useState<boolean>(false);

  // 日期选择器状态
  const [selectedDate, setSelectedDate] = useState<Date | null>(null); // 初始为null，表示未选择日期
  const [datePickerVisible, setDatePickerVisible] = useState<boolean>(false);

  // 缩略图状态
  const [videoThumbnails, setVideoThumbnails] = useState<Map<string, string>>(new Map()); // 存储每个视频的缩略图

  // 编辑模态框状态
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);

  // 获取缩略图 - 支持二进制响应
  const getThumbnailWithBinary = (params: {path: string, size: string}) => {
    return request.post('/filemgr/get_thumbnail', params, { responseType: 'arraybuffer' });
  };

  // 获取所有视频的缩略图 - 串行版本
  const fetchAllThumbnails = useCallback(async () => {
    if (videoData && videoData.length > 0) {
      try {
        // 创建临时存储数组
        const thumbnailResults: Array<{ videoKey: string; imageUrl: string }> = [];

        // 串行获取每个缩略图，避免并发请求导致进程卡死
        for (const video of videoData) {
          try {
            const res = await getThumbnailWithBinary({
              path: video.file,
              size: "medium"
            });

            if (res instanceof ArrayBuffer) {
              // 检查JPEG文件头
              const uint8Array = new Uint8Array(res);
              const isValidJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF;

              let imageUrl: string;
              if (isValidJPEG) {
                const blob = new Blob([res], { type: 'image/jpeg' });
                imageUrl = URL.createObjectURL(blob);
              } else {
                // 尝试作为其他格式
                const blob = new Blob([res], { type: 'image/png' });
                imageUrl = URL.createObjectURL(blob);
              }

              // 将成功获取的缩略图存储到临时数组
              const videoKey = `${video.camera_lens}-${video.time}`;
              thumbnailResults.push({ videoKey, imageUrl });
            }
          } catch (error) {
            console.error(`获取视频缩略图失败:`, error);
            // 单个失败不影响其他缩略图的获取，继续处理下一个
          }
        }

        // 等待所有请求完成后，一次性创建新的Map并更新状态，避免闪屏
        const newThumbnailMap = new Map<string, string>();
        thumbnailResults.forEach(result => {
          newThumbnailMap.set(result.videoKey, result.imageUrl);
        });

        // 一次性更新状态
        if (newThumbnailMap.size > 0) {
          setVideoThumbnails(newThumbnailMap);
        }

      } catch (error) {
        console.error('批量获取缩略图失败:', error);
      }
    }
  }, [videoData]);

  // 清理blob URLs
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有blob URLs
      videoThumbnails.forEach((url) => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [videoThumbnails]);

  // 请求摄像机列表
 useRequest(
    () => listRecordCamera({ did: [] }),
    {
      onSuccess: (res) => {
        if (res.code === 0 && res.data?.camera) {
          const cameras = res.data.camera;
          setCameraList(cameras);

          // 转换为选项格式，使用model字段作为显示名称
          const options: CameraOption[] = cameras.map((camera) => ({
            label: camera.model,
            value: camera.did,
          }));

          if (options.length > 0) {
            setCameraOptions(options);
            // 如果当前选择的摄像机不在新列表中，选择第一个
            if (!options.find(opt => opt.value === selectedCamera)) {
              setSelectedCamera(options[0].value);
            }
          }
        }
      },
      onError: (err) => {
        console.error("获取摄像机列表失败:", err);
        // 保持使用默认数据
        setCameraOptions(defaultCameraOptions);
      },
    }
  );

  // 生成camera_lens参数
  const generateCameraLensParams = useMemo(() => {
    const selectedCameraInfo = cameraList.find(camera => camera.did === selectedCamera);
    if (selectedCameraInfo && selectedCameraInfo.key_frame.length > 0) {
      return selectedCameraInfo.key_frame.map(frame => `${selectedCameraInfo.did}_${frame.lens_id}`);
    }
    return [""]; 
  }, [selectedCamera, cameraList]);

  // 请求视频数据
  const { run: fetchVideoData } = useRequest(
    (params: any) => getVideoRecord(params),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data?.videos) {
          const videos = res.data.videos.map((video) => ({
            ...video,
            timeLabel: generateTimeRangeLabel(video.time, video.media_duration),
            thumbnail: "", // 暂时使用占位图
          }));
          setVideoData(videos);
          setPageInfo(res.data.page);
        }
      },
      onError: (err) => {
        console.error("获取视频数据失败:", err);
        // 保持使用默认数据
        setVideoData(mockVideoData);
      },
    }
  );

  // 当视频数据更新后延迟获取缩略图
  useEffect(() => {
    if (videoData && videoData.length > 0) {
      // 延迟500毫秒开始获取缩略图，等待组件渲染完成
      const timer = setTimeout(() => {
        fetchAllThumbnails();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [videoData, fetchAllThumbnails]);

  // 获取当前选择的摄像机标签
  const selectedCameraLabel = useMemo(() => {
    const camera = cameraOptions?.find(option => option.value === selectedCamera);
    return camera?.label || "摄像机01";
  }, [selectedCamera, cameraOptions]);

  // 格式化日期显示
  const dateDisplayText = useMemo(() => {
    if (!selectedDate) {
      return "选择日期"; // 未选择日期时的默认显示
    }
    const today = new Date();
    const isToday = selectedDate.toDateString() === today.toDateString();
    return isToday ? "今天" : format(selectedDate, 'M月dd日', { locale: zhCN });
  }, [selectedDate]);

  // 当摄像机变化时，获取视频数据（初始调用，不带时间参数）
  useEffect(() => {
    if (generateCameraLensParams.length > 0) {
      const params = {
        page: pageInfo,
        options: {
          option: ["camera_lens", "event_name"],
          camera_lens: generateCameraLensParams,
        },
      };
      fetchVideoData(params);
    }
  }, [selectedCamera, generateCameraLensParams, pageInfo, fetchVideoData]);

  // 当日期选择后，获取指定日期的视频数据
  useEffect(() => {
    if (selectedDate && generateCameraLensParams.length > 0) {
      // 生成时间范围（选中日期的00:00:00到23:59:59）
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      const params = {
        page: pageInfo,
        options: {
          option: ["camera_lens", "event_name", "time"],
          camera_lens: generateCameraLensParams,
          time: {
            start: Math.floor(startOfDay.getTime() / 1000).toString(),
            end: Math.floor(endOfDay.getTime() / 1000).toString(),
          },
        },
      };
      fetchVideoData(params);
    }
  }, [selectedDate, generateCameraLensParams, pageInfo, fetchVideoData]);

  const handleBack = () => {
    history.goBack()
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setDatePickerVisible(false);
  };

  // 打开编辑模态框
  const handleOpenEditModal = () => {
    setEditModalVisible(true);
  };

  // 关闭编辑模态框
  const handleCloseEditModal = () => {
    setEditModalVisible(false);
  };

  // 保存到本机
  const handleSaveToLocal = (selectedVideos: VideoItem[]) => {
    console.log('保存到本机:', selectedVideos);
    // TODO: 实现保存到本机的逻辑
  };

  // 删除视频
  const handleDeleteVideos = (selectedVideos: VideoItem[]) => {
    console.log('删除视频:', selectedVideos);
    // TODO: 实现删除视频的逻辑
  };

  // 生成时间范围标签（开始时间-结束时间）
  const generateTimeRangeLabel = (startTimestamp: string, durationSeconds: number): string => {
    try {
      const startTime = parseInt(startTimestamp) * 1000;
      const endTime = startTime + (durationSeconds * 1000);

      const startDate = new Date(startTime);
      const endDate = new Date(endTime);

      const startTimeStr = format(startDate, 'HH:mm');
      const endTimeStr = format(endDate, 'HH:mm');

      return `${startTimeStr}-${endTimeStr}`;
    } catch (error) {
      console.error('时间范围标签生成失败:', error);
      return '00:00-00:00';
    }
  };

  const renderVideoItem = (video: VideoItem, index: number) => {
    const videoKey = `${video.camera_lens}-${video.time}`;
    const thumbnailUrl = videoThumbnails.get(videoKey);

    return (
      <div key={`${video.camera_lens}-${video.time}-${index}`} className={styles.videoItem}>
        <div className={styles.thumbnailContainer}>
          <div className={styles.thumbnail}>
            {thumbnailUrl ? (
              // 显示真实的缩略图
              <img
                src={thumbnailUrl}
                alt="视频缩略图"
                className={styles.thumbnailImage}
                onError={(e) => {
                  // 如果图片加载失败，显示占位图
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove(styles.hidden);
                }}
              />
            ) : null}
            {/* 占位图或加载状态 */}
            <div className={`${styles.thumbnailPlaceholder} ${thumbnailUrl ? styles.hidden : ''}`}>
            </div>

            {/* 播放图标  */}
            {thumbnailUrl && (
              <div className={styles.playIcon}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.8)" />
                  <polygon points="10,8 16,12 10,16" fill="#333" />
                </svg>
              </div>
            )}
          </div>
        </div>
        <div className={styles.timeLabel}>{generateTimeRangeLabel(video.time, video.media_duration) || video.timeLabel }</div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <NavigatorBar
        onBack={handleBack}
        right={
          <div onClick={handleOpenEditModal} style={{ cursor: 'pointer' }}>
            <PreloadImage
              src={isDarkMode ? filterDarkIcon : filterIcon}
            />
          </div>
        }
      />
      <div className={styles.title}>存储管理</div>

      {/* 选择器容器 */}
      <div className={styles.selectorContainer}>
        {/* 摄像机选择器 */}
        <PopoverSelector
          visible={cameraPopoverVisible}
          onVisibleChange={setCameraPopoverVisible}
          value={selectedCamera}
          options={cameraOptions}
          onChange={setSelectedCamera}
          placement="bottom-start"
        >
          <div className={styles.selector}>
            <span className={styles.selectorText}>{selectedCameraLabel}</span>
            <PreloadImage
              src={dateSelect}
              className={styles.selectorArrow}
            />
          </div>
        </PopoverSelector>

        {/* 日期选择器 */}
        <div className={styles.selector} onClick={() => setDatePickerVisible(true)}>
          <span className={styles.selectorText}>{dateDisplayText}</span>
          <PreloadImage
            src={dateSelect}
            className={styles.selectorArrow}
          />
        </div>
      </div>

      {/* 视频网格 */}
      <div className={styles.content}>
        <div className={styles.videoGrid}>
          {videoData.map((video, index) => renderVideoItem(video, index))}
        </div>
      </div>

      {/* 日期选择器弹窗 */}
      <DatePicker
        isShow={datePickerVisible}
        onCancel={() => setDatePickerVisible(false)}
        onSelect={handleDateSelect}
      />

      {/* 编辑模态框 */}
      <VideoEditModal
        visible={editModalVisible}
        onClose={handleCloseEditModal}
        videoData={videoData}
        videoThumbnails={videoThumbnails}
        onSaveToLocal={handleSaveToLocal}
        onDelete={handleDeleteVideos}
        generateTimeRangeLabel={generateTimeRangeLabel}
      />
    </div>
  );
}
